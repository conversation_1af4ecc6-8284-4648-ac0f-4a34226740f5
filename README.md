# Campaign Keywords Pipeline

A sophisticated healthcare data processing pipeline that uses AI/LLM to generate relevant educational content keywords based on patient clinical data (medical problems). The system implements hash-based change detection to optimize performance and reduce unnecessary API calls.

## 🏗️ System Architecture

### Core Components

1. **Main Pipeline** (`main.py`, `src/pipeline.py`) - Orchestrates the entire keyword generation process
2. **API Gateway** (`api_server/api_server.py`) - Central routing service for Gemini LLM API calls
3. **Database Layer** (`src/db.py`) - Handles all database operations with SQL Server
4. **Hash-Based Change Detection** (`src/hashing.py`) - Optimizes processing by detecting data changes
5. **Configuration Management** (`config.py`) - Centralized configuration with validation
6. **Health Monitoring** (`src/health_check.py`) - HTTP health check endpoints
7. **Logging System** (`src/logging_config.py`) - Structured logging with rotation

### Database Architecture

- **Primary Table**: `dbo.Appointment_EducationalContent_Keyword`
  - Stores appointment records with generated keywords
  - Contains hash values for change detection
  - Tracks reasoning and metadata
- **Clinical Data**: `dbo.Patient_Problem` (Problems/diagnoses only)
- **Supporting Tables**: `dbo.Appointments` (for appointment details)

## 🔄 Processing Flow

### 1. Data Ingestion
```
Appointments → Patient Problems → Hash Calculation → Change Detection
```

### 2. Hash-Based Change Detection
- **Purpose**: Avoid unnecessary LLM calls for unchanged patient data
- **Process**: 
  1. Fetch patient's clinical data (Problems)
  2. Canonicalize data structure for consistent hashing
  3. Generate SHA-256 hash of clinical data
  4. Compare with previously stored hash
  5. Skip LLM processing if data hasn't changed

### 3. LLM Processing (Gemini AI)
- **Input**: Patient problems + campaign keywords list
- **Output**: Top 7 relevant keywords + reasoning
- **API**: Google Gemini 1.5 Flash via central API gateway

### 4. Data Storage
- **Keywords**: Comma-separated list of selected keywords
- **Reasoning**: LLM's explanation for keyword selection
- **Hash**: SHA-256 hash for future change detection
- **Metadata**: Timestamps, source information

## 📁 Project Structure

```
keywordsV2/
├── main.py                     # Application entry point
├── config.py                   # Configuration management
├── requirements.txt            # Python dependencies
├── README.md                   # This file
├── data/
│   └── CampaignKeywords.csv   # List of available keywords
├── sql/
│   └── stored_procedures.sql   # Database procedures
├── api_server/
│   ├── api_server.py          # Gemini API gateway service
│   ├── logging_config.py      # API server logging setup
│   └── logs/                  # API server log files
├── src/
│   ├── __init__.py
│   ├── pipeline.py            # Main processing logic
│   ├── db.py                  # Database operations
│   ├── hashing.py             # Change detection utilities
│   ├── llm_langchain.py       # LLM integration
│   ├── health_check.py        # Health monitoring
│   ├── logging_config.py      # Client service logging setup
│   └── db_pool.py             # Connection pooling
└── logs/                      # Client service log files
```

## 🚀 Quick Start Guide

### Prerequisites

1. **Python 3.8+** installed
2. **SQL Server** database access
3. **Google Gemini API key**
4. **ODBC Driver 17/18 for SQL Server**

### Step 1: Environment Setup

1. **Clone and navigate to project**:
   ```bash
   cd keywordsV2
   ```

2. **Install Python dependencies**:
   ```bash
   pip3 install -r requirements.txt
   ```

3. **Create environment file** (`.env`):
   ```bash
   # Database Configuration
   DB_DRIVER={ODBC Driver 17 for SQL Server}
   DB_SERVER=your_server.database.windows.net
   DB_DATABASE=your_database_name
   DB_USERNAME=your_username
   DB_PASSWORD=your_password
   DB_ENCRYPT=yes
   DB_TRUST_CERT=no
   
   # API Gateway Configuration
   API_GATEWAY_URL=http://localhost:8000/gemini/query
   API_GATEWAY_TOKEN=your_jwt_token
   
   # Gemini API (for API server)
   GEMINI_API_KEY=your_gemini_api_key
   JWT_SECRET_KEY=your-super-secret-jwt-key
   
   # Pipeline Settings
   BATCH_SIZE=50
   PROCESS_DATE=2024-01-15  # Optional: specific date or leave empty
   CAMPAIGN_KEYWORDS_CSV=data/CampaignKeywords.csv
   ```

### Step 2: Database Setup

1. **Run stored procedures script**:
   ```sql
   -- Execute sql/stored_procedures.sql in your SQL Server database
   -- This creates all required stored procedures
   ```

2. **Verify required tables exist**:
   - `dbo.Appointment_EducationalContent_Keyword`
   - `dbo.Patient_Problem`
   - `dbo.Appointments`

3. **Ensure table has required columns**:
   ```sql
   -- Verify these columns exist in Appointment_EducationalContent_Keyword:
   ALTER TABLE dbo.Appointment_EducationalContent_Keyword 
   ADD Hashkey NVARCHAR(64),
       Reasoning NVARCHAR(MAX),
       KeywordLastUpdated DATETIME;
   ```

### Step 3: Start API Gateway (Terminal 1)

```bash
cd api_server
python3 api_server.py
```

**Expected output**:
```
INFO:     Starting Gemini API Routing Service on 0.0.0.0:8000
INFO:     Database: your_server/your_database
INFO:     JWT expiration: 60 minutes
INFO:     Uvicorn running on http://0.0.0.0:8000
```

**Health check**: Visit `http://localhost:8000/health`

### Step 4: Start Main Pipeline (Terminal 2)

```bash
python3 main.py
```

**Expected output**:
```
2024-01-15 10:00:00 - INFO - Starting health check API on http://0.0.0.0:8080
2024-01-15 10:00:00 - INFO - 🚀 Starting Appointment Keyword Pipeline with LangChain
2024-01-15 10:00:00 - INFO - Loaded 160 unique campaign keywords
2024-01-15 10:00:00 - INFO - Fetching appointment batch. Offset: 0, Size: 50
```

## 🔧 Configuration Options

### Environment Variables

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `DB_SERVER` | SQL Server hostname | - | ✅ |
| `DB_DATABASE` | Database name | - | ✅ |
| `DB_USERNAME` | Database username | - | ✅ |
| `DB_PASSWORD` | Database password | - | ✅ |
| `GEMINI_API_KEY` | Google Gemini API key | - | ✅ |
| `API_GATEWAY_URL` | API gateway endpoint | `http://localhost:8000/gemini/query` | ✅ |
| `API_GATEWAY_TOKEN` | JWT token for API auth | - | ✅ |
| `BATCH_SIZE` | Records per batch | `50` | ❌ |
| `PROCESS_DATE` | Specific date filter | `None` (all dates) | ❌ |

### Pipeline Behavior

- **Batch Processing**: Processes appointments in configurable batches
- **Change Detection**: Skips patients with unchanged clinical data
- **Error Handling**: Continues processing on individual failures
- **Graceful Shutdown**: Handles SIGINT/SIGTERM signals
- **Health Monitoring**: HTTP endpoints for status checks

## 📊 Monitoring & Logging

### Log Files

**Client Service** (in `logs/` directory):
- `client_service.log` - All client service operations (pipeline, database, health checks)

**API Server** (in `api_server/logs/` directory):
- `api_server.log` - All API server operations (requests, responses, authentication)

### Health Endpoints

- **Pipeline Health**: `http://localhost:8080/health`
- **API Gateway Health**: `http://localhost:8000/health`
- **Detailed Status**: `http://localhost:8080/status`

### Key Metrics to Monitor

- **Processing Rate**: Appointments processed per minute
- **Hash Hit Rate**: Percentage of patients skipped due to unchanged data
- **LLM Success Rate**: Successful keyword generations
- **Database Performance**: Query execution times
- **API Response Times**: Gemini API call latencies

## 🔍 Troubleshooting

### Common Issues

1. **Database Connection Errors**
   ```
   Error: [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Login failed
   ```
   - Verify database credentials in `.env`
   - Check network connectivity to SQL Server
   - Ensure ODBC driver is installed

2. **Gemini API Errors**
   ```
   Error: 401 Unauthorized
   ```
   - Verify `GEMINI_API_KEY` is correct
   - Check API quota and billing status

3. **Import Errors**
   ```
   ModuleNotFoundError: No module named 'dotenv'
   ```
   - Run: `pip3 install -r requirements.txt`

4. **No Data Processing**
   ```
   INFO: Batch contains no new or changed patient data
   ```
   - Check if `PROCESS_DATE` filter is too restrictive
   - Verify appointments exist in database
   - Check if all patients already processed (hash matches)

### Debug Mode

Enable detailed logging:
```bash
export LOG_LEVEL=DEBUG
python3 main.py
```

## 🧪 Testing

### Manual Testing

1. **Test API Gateway**:
   ```bash
   curl -X GET http://localhost:8000/health
   ```

2. **Test Pipeline Health**:
   ```bash
   curl -X GET http://localhost:8080/health
   ```

3. **Check Database Connectivity**:
   ```python
   python3 -c "import config; print('Config loaded successfully')"
   ```

### Automated Testing

```bash
# Run tests (when available)
pytest tests/
```

## 📈 Performance Optimization

### Hash-Based Change Detection Benefits

- **Reduced LLM Calls**: ~70-90% reduction in API calls for stable patients
- **Faster Processing**: Skip unchanged patients entirely
- **Cost Savings**: Significant reduction in Gemini API costs
- **Consistent Results**: Same keywords for unchanged clinical data

### Scaling Considerations

- **Batch Size**: Increase for better throughput, decrease for lower memory usage
- **Connection Pooling**: Implemented for database efficiency
- **Parallel Processing**: Consider multi-threading for large datasets
- **API Rate Limits**: Monitor Gemini API quotas and implement backoff

## 🔐 Security

- **Environment Variables**: Sensitive data stored in `.env` file
- **JWT Authentication**: API gateway protected with tokens
- **Database Security**: Uses parameterized queries to prevent SQL injection
- **Privacy**: Patient data hashed for logging (PID_xxx format)

## 🤝 Contributing

1. Follow existing code style and patterns
2. Add comprehensive logging for new features
3. Update documentation for configuration changes
4. Test with small batches before full deployment
5. Monitor performance impact of changes

## � Technical Deep Dive

### Hash-Based Change Detection Algorithm

The system uses SHA-256 hashing to detect changes in patient clinical data:

```python
# 1. Data Canonicalization
canonical_data = {
    "Problems": sorted_problems_list  # Sorted by ProblemName, Type, DetailText
}

# 2. Hash Generation
current_hash = sha256(json.dumps(canonical_data, sort_keys=True)).hexdigest()

# 3. Change Detection
if current_hash == stored_hash:
    skip_processing()  # No changes detected
else:
    process_with_llm()  # Changes detected
```

### LLM Prompt Engineering

The system uses structured prompts for consistent keyword extraction:

```
Input: Patient problems + 160 campaign keywords
Output: {
  "patient_123_456": {
    "keywords": ["keyword1", "keyword2", ...],
    "reasoning": "Step-by-step explanation..."
  }
}
```

### Database Schema Requirements

```sql
-- Required columns in Appointment_EducationalContent_Keyword
PatientID BIGINT NOT NULL
ApptID BIGINT NOT NULL
[Keyword] NVARCHAR(2000)
Reasoning NVARCHAR(MAX)
Hashkey NVARCHAR(64)          -- SHA-256 hash
KeywordLastUpdated DATETIME
[Source] NVARCHAR(10)
[KeywordSource] NVARCHAR(25)
[ModifiedBy] BIGINT
[ModifiedUTCDTTM] DATETIME
CreatedUTCDTTM DATETIME
```

## 🚨 Production Deployment

### Environment-Specific Configuration

**Development**:
```bash
LOG_LEVEL=DEBUG
BATCH_SIZE=10
API_SERVER_HOST=localhost
```

**Production**:
```bash
LOG_LEVEL=INFO
BATCH_SIZE=100
API_SERVER_HOST=0.0.0.0
MAX_REQUESTS_PER_MINUTE=300
```

### Monitoring Setup

1. **Application Metrics**:
   - Processing rate (appointments/minute)
   - Hash hit rate (% skipped due to no changes)
   - LLM success rate
   - Database query performance

2. **Infrastructure Metrics**:
   - CPU and memory usage
   - Database connection pool status
   - API response times
   - Error rates

3. **Business Metrics**:
   - Total appointments processed
   - Keywords generated per day
   - Cost per API call
   - Data freshness (time since last update)

### Backup and Recovery

- **Database**: Regular backups of keyword data and hashes
- **Configuration**: Version control for all config files
- **Logs**: Centralized log aggregation for troubleshooting
- **API Keys**: Secure key rotation procedures

## 🔄 Maintenance Tasks

### Daily
- Monitor processing logs for errors
- Check API quota usage
- Verify health endpoints

### Weekly
- Review hash hit rates and performance
- Analyze keyword generation patterns
- Check database growth and cleanup old logs

### Monthly
- Update dependencies (`pip install -r requirements.txt --upgrade`)
- Review and rotate API keys
- Performance optimization analysis

## 📊 Sample Output

### Successful Processing Log
```
2024-01-15 10:00:00 - INFO - 🚀 Starting Appointment Keyword Pipeline
2024-01-15 10:00:01 - INFO - Loaded 160 unique campaign keywords
2024-01-15 10:00:02 - INFO - Fetching appointment batch. Offset: 0, Size: 50
2024-01-15 10:00:03 - INFO - Fetched 50 appointments
2024-01-15 10:00:04 - INFO - Skipping PatientID 12345: clinical data unchanged (Hash: a1b2c3d4e5...)
2024-01-15 10:00:05 - INFO - Processing 15 patients with changed clinical data
2024-01-15 10:00:10 - INFO - Successfully generated keywords for 15 patients
2024-01-15 10:00:11 - INFO - Updated 45 appointment records in database
2024-01-15 10:00:12 - INFO - Batch processing complete. Hash hit rate: 70%
```

### Generated Keywords Example
```json
{
  "patient_12345_67890": {
    "keywords": [
      "ulcerative colitis",
      "inflammatory bowel disease",
      "Entyvio (ulcerative colitis)",
      "rectal bleeding",
      "abdominal pain",
      "diarrhea",
      "gastroenterology"
    ],
    "reasoning": "Patient has active ulcerative colitis with symptoms of rectal bleeding and abdominal pain. Entyvio is a relevant treatment option for moderate to severe UC. Keywords selected based on primary diagnosis and associated symptoms."
  }
}
```

## �📞 Support

For issues or questions:
1. Check logs in `logs/` directory for detailed error information
2. Verify configuration in `.env` file matches your environment
3. Test individual components:
   - Database connection: `python3 -c "import config; print('Config loaded')"`
   - API gateway: `curl http://localhost:8000/health`
   - Pipeline health: `curl http://localhost:8080/health`
4. Review this README for common solutions and troubleshooting steps
5. Check database permissions and stored procedure existence
6. Verify Gemini API key validity and quota limits
