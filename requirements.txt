# Core Python dependencies for Campaign Keywords Pipeline

# Environment and Configuration
python-dotenv>=1.0.0

# Database connectivity
pyodbc>=4.0.39
sqlalchemy>=1.4.0

# HTTP requests and API
requests>=2.31.0
fastapi>=0.104.0
uvicorn>=0.24.0
pydantic>=2.0.0

# JWT authentication
pyjwt>=2.8.0
python-jose[cryptography]>=3.3.0

# Google Gemini API
google-generativeai>=0.3.0

# Token counting (optional but recommended)
tiktoken>=0.5.0

# CORS middleware
fastapi[all]>=0.104.0

# Logging and utilities
pathlib2>=2.3.7; python_version < "3.4"

# Development and testing (optional)
pytest>=7.4.0
pytest-asyncio>=0.21.0
black>=23.0.0
flake8>=6.0.0
mypy>=1.5.0

# Additional database drivers (if needed)
# pymssql>=2.2.8  # Alternative SQL Server driver
# psycopg2-binary>=2.9.7  # PostgreSQL driver
