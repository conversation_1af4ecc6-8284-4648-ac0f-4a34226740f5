"""
Health check API module for the Campaign Keywords Pipeline.

This module provides a simple HTTP health check endpoint that can be used
for monitoring and load balancer health checks.
"""

import logging
import threading
from datetime import datetime
from typing import Dict, Any
import json

try:
    from http.server import HTTPServer, BaseHTTPRequestHandler
    from urllib.parse import urlparse, parse_qs
except ImportError:
    # Python 2 compatibility (though this project uses Python 3)
    from BaseHTTPServer import HTTPServer, BaseHTTPRequestHandler
    from urlparse import urlparse, parse_qs

logger = logging.getLogger("health_check")


class HealthCheckHandler(BaseHTTPRequestHandler):
    """HTTP request handler for health check endpoints."""
    
    def do_GET(self):
        """Handle GET requests for health checks."""
        parsed_path = urlparse(self.path)
        
        if parsed_path.path == '/health':
            self._handle_health_check()
        elif parsed_path.path == '/status':
            self._handle_status_check()
        else:
            self._send_error(404, "Not Found")
    
    def _handle_health_check(self):
        """Handle basic health check endpoint."""
        response_data = {
            "status": "healthy",
            "timestamp": datetime.utcnow().isoformat(),
            "service": "campaign-keywords-pipeline",
            "version": "1.0.0"
        }
        self._send_json_response(200, response_data)
    
    def _handle_status_check(self):
        """Handle detailed status check endpoint."""
        # You can add more detailed checks here
        response_data = {
            "status": "healthy",
            "timestamp": datetime.utcnow().isoformat(),
            "service": "campaign-keywords-pipeline",
            "version": "1.0.0",
            "checks": {
                "database": "unknown",  # Could add DB connectivity check
                "api_gateway": "unknown",  # Could add API gateway check
                "memory": "ok",
                "disk": "ok"
            }
        }
        self._send_json_response(200, response_data)
    
    def _send_json_response(self, status_code: int, data: Dict[str, Any]):
        """Send a JSON response."""
        self.send_response(status_code)
        self.send_header('Content-Type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        
        response_json = json.dumps(data, indent=2)
        self.wfile.write(response_json.encode('utf-8'))
    
    def _send_error(self, status_code: int, message: str):
        """Send an error response."""
        error_data = {
            "error": message,
            "status_code": status_code,
            "timestamp": datetime.utcnow().isoformat()
        }
        self._send_json_response(status_code, error_data)
    
    def log_message(self, format, *args):
        """Override to use our logger instead of stderr."""
        logger.info(f"{self.address_string()} - {format % args}")


def start_health_api(host: str = "0.0.0.0", port: int = 8080):
    """
    Start the health check API server in a background thread.
    
    Args:
        host: Host to bind to (default: "0.0.0.0")
        port: Port to bind to (default: 8080)
    """
    def run_server():
        try:
            server = HTTPServer((host, port), HealthCheckHandler)
            logger.info(f"Health check API started on http://{host}:{port}")
            logger.info("Available endpoints:")
            logger.info(f"  - http://{host}:{port}/health - Basic health check")
            logger.info(f"  - http://{host}:{port}/status - Detailed status check")
            server.serve_forever()
        except Exception as e:
            logger.error(f"Failed to start health check API: {e}")
    
    # Start server in background thread
    health_thread = threading.Thread(target=run_server, daemon=True)
    health_thread.start()
    
    return health_thread


if __name__ == "__main__":
    # For testing the health check API standalone
    import sys
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    port = int(sys.argv[1]) if len(sys.argv) > 1 else 8080
    
    logger.info(f"Starting health check API on port {port}")
    thread = start_health_api(port=port)
    
    try:
        # Keep main thread alive
        thread.join()
    except KeyboardInterrupt:
        logger.info("Shutting down health check API")
        sys.exit(0)
