# src/pipeline.py (MODIFIED)
import logging
import signal
import config
from src import db
from src import llm_langchain # Use our new LangChain module
from src import hashing # Use your hashing module
from datetime import datetime, date

logger = logging.getLogger("patient_pipeline")
shutdown_requested = False

def signal_handler(signum, _):
    global shutdown_requested
    logger.info(f"Received signal {signum}. Requesting graceful shutdown...")
    shutdown_requested = True

signal.signal(signal.SIGINT, signal_handler)
signal.signal(signal.SIGTERM, signal_handler)

def datetime_serializer(obj):
    if isinstance(obj, (datetime, date)):
        return obj.isoformat()
    raise TypeError(f"Object of type {type(obj)} is not JSON serializable")

def main():
    logger.info("🚀 Starting Appointment Keyword Pipeline with LangChain")

    # --- Load Campaign Keywords ---
    try:
        with open(config.CAMPAIGN_KEYWORDS_CSV, 'r', encoding='utf-8') as f:
            # Simple list comprehension to get a flat list of keywords
            import csv
            reader = csv.reader(f)
            next(reader) # Skip header
            campaign_kw = sorted(list(set(kw.strip().lower() for row in reader for kw in row if kw.strip())))
        logger.info(f"Loaded {len(campaign_kw)} unique campaign keywords.")
    except Exception as e:
        logger.error(f"Fatal: Could not load campaign keywords from {config.CAMPAIGN_KEYWORDS_CSV}. Error: {e}")
        return

    offset = 0
    total_processed_appointments = 0
    
    # --- Main Processing Loop ---
    while not shutdown_requested:
        # 1. Fetch a batch of appointments
        try:
            appointments = db.fetch_appointment_batch(offset, config.BATCH_SIZE)
            if not appointments:
                logger.info("No more appointments to process. Pipeline finished.")
                break
        except Exception as e:
            logger.error(f"Error fetching appointments at offset {offset}: {e}. Skipping to next batch.")
            offset += config.BATCH_SIZE
            continue

        # Create a dictionary to hold data for patients needing an LLM call
        patients_to_process = {}
        patient_ids_in_batch = list(set(appt['PatientID'] for appt in appointments))
        
        # 2. Fetch clinical data (problems) for the batch
        try:
            patient_problems = db.fetch_patient_problems(patient_ids_in_batch)
        except Exception as e:
            logger.error(f"Could not fetch patient problems for batch at offset {offset}: {e}")
            offset += config.BATCH_SIZE
            continue

        # 3. Hash data and check for changes
        for appt in appointments:
            patient_id = appt['PatientID']
            appt_id = appt['ApptID']
            
            clinical_data = {
                "Problems": patient_problems.get(patient_id, [])
                # Only Problems data is used for keyword generation
            }
            
            # Canonicalize and hash the data for consistency
            canonical_data = hashing.canonicalize_patient_data(clinical_data)
            current_hash = hashing.stable_hash(canonical_data)
            
            latest_stored_hash = db.get_latest_hash(patient_id)

            if current_hash == latest_stored_hash:
                logger.info(f"Skipping PatientID {patient_id}: clinical data has not changed (Hash: {current_hash[:10]}...).")
                continue
            
            # This patient needs processing. Add their data.
            # Use a composite key to handle multiple appointments for one patient in a batch
            appt_key = f"{patient_id}_{appt_id}"
            patients_to_process[appt_key] = {
                "data": canonical_data,
                "hash": current_hash
            }

        if not patients_to_process:
            logger.info(f"Batch at offset {offset} contains no new or changed patient data. Moving to next batch.")
            offset += config.BATCH_SIZE
            continue

        # 4. Call LLM for patients that need processing
        try:
            # Prepare data just for the LLM
            llm_payload = {key: value['data'] for key, value in patients_to_process.items()}
            llm_results = llm_langchain.generate_keywords_for_batch(llm_payload, campaign_kw)
        except Exception as e:
            logger.error(f"LLM processing failed for batch at offset {offset}: {e}")
            offset += config.BATCH_SIZE
            continue

        # 5. Update Database with results
        updated_count = 0
        for key, result in llm_results.patient_results.items():
            try:
                patient_id_str, appt_id_str = key.split('_')
                patient_id = int(patient_id_str)
                appt_id = int(appt_id_str)
                
                # Get the hash we calculated earlier for this appointment
                data_hash = patients_to_process[key]['hash']

                # Convert keyword list to a comma-separated string for DB
                keywords_str = ", ".join(result.keywords)
                
                db.update_appointment_keywords(
                    patient_id=patient_id,
                    appt_id=appt_id,
                    keywords=keywords_str,
                    reasoning=result.reasoning,
                    hashkey=data_hash
                )
                updated_count += 1
            except Exception as e:
                logger.error(f"Failed to update database for appointment key {key}: {e}")

        logger.info(f"Successfully updated {updated_count} of {len(llm_results.patient_results)} appointments in the database.")
        total_processed_appointments += updated_count
        offset += config.BATCH_SIZE
        
    logger.info(f"Pipeline finished. Total appointments processed and updated: {total_processed_appointments}.")