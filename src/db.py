# src/db.py
import pyodbc
import config
import logging
from typing import List, Dict, Any

logger = logging.getLogger("patient_pipeline")

def get_db_connection():
    """Establishes and returns a database connection."""
    conn_str = (
        f"DRIVER={config.DB_DRIVER};"
        f"SERVER={config.DB_SERVER};"
        f"DATABASE={config.DB_DATABASE};"
        f"UID={config.DB_USERNAME};"
        f"PWD={config.DB_PASSWORD};"
        f"Encrypt={config.DB_ENCRYPT};"
        f"TrustServerCertificate={config.DB_TRUST_CERT};"
        "Connection Timeout=30;"
    )
    return pyodbc.connect(conn_str)

def fetch_appointment_batch(offset: int, batch_size: int) -> List[Dict[str, Any]]:
    """Fetches a batch of appointments using sp_GetAppointmentBatch."""
    logger.info(f"Fetching appointment batch. Offset: {offset}, Size: {batch_size}")
    appointments = []
    with get_db_connection() as conn:
        cursor = conn.cursor()
        # The stored procedure name must match exactly.
        # Parameters must be passed in the correct order.
        sql = "{CALL sp_GetAppointmentBatch (?, ?, ?)}"
        params = (offset, batch_size, config.PROCESS_DATE)
        cursor.execute(sql, params)
        
        columns = [column[0] for column in cursor.description]
        for row in cursor.fetchall():
            appointments.append(dict(zip(columns, row)))
            
    logger.info(f"Fetched {len(appointments)} appointments.")
    return appointments

def fetch_patient_problems(patient_ids: List[int]) -> Dict[int, List[Dict[str, Any]]]:
    """Fetches all problems for a list of patient IDs."""
    if not patient_ids:
        return {}

    logger.debug(f"Fetching problems for {len(patient_ids)} patients.")
    problems_by_patient = {pid: [] for pid in patient_ids}
    
    # Create a comma-separated string of patient IDs for the IN clause
    id_placeholders = ','.join(['?'] * len(patient_ids))
    
    sql = f"""
        SELECT
            PatientID,
            'Problem' as DataType,
            ProblemName as Name,
            Type as Value1,
            DetailText as Value2,
            NULL as Value3
        FROM dbo.Patient_Problem
        WHERE PatientID IN ({id_placeholders})
    """

    with get_db_connection() as conn:
        cursor = conn.cursor()
        cursor.execute(sql, patient_ids)
        
        columns = [column[0] for column in cursor.description]
        for row in cursor.fetchall():
            patient_id = row.PatientID
            if patient_id in problems_by_patient:
                problems_by_patient[patient_id].append(dict(zip(columns, row)))
    
    return problems_by_patient

def get_latest_hash(patient_id: int) -> str | None:
    """Retrieves the most recent data hash for a patient."""
    with get_db_connection() as conn:
        cursor = conn.cursor()
        sql = "{CALL sp_GetPatientLatestHash (?)}"
        cursor.execute(sql, (patient_id,))
        row = cursor.fetchone()
        return row.Hashkey if row else None

def update_appointment_keywords(patient_id: int, appt_id: int, keywords: str, reasoning: str, hashkey: str):
    """Updates keywords and reasoning for a single appointment."""
    logger.debug(f"Updating DB for PatientID: {patient_id}, ApptID: {appt_id}")
    with get_db_connection() as conn:
        cursor = conn.cursor()
        sql = "{CALL sp_UpdateAppointmentKeywords (?, ?, ?, ?, ?)}"
        params = (
            patient_id,
            appt_id,
            keywords,
            reasoning,
            hashkey
        )
        cursor.execute(sql, params)
        conn.commit()