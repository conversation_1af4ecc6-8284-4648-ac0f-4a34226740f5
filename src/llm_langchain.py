# src/llm_langchain.py
import logging
import requests
import json
from typing import List, Dict, Any
from pydantic import BaseModel, Field

import config

logger = logging.getLogger("patient_pipeline")

# --- Pydantic Models for Structured LLM Output ---
# This defines the exact structure we want the LLM to return.
class KeywordResult(BaseModel):
    """The result for a single patient."""
    keywords: List[str] = Field(description="List of the top 7 most relevant keywords from the provided list.")
    reasoning: str = Field(description="A concise, step-by-step reasoning for why these specific keywords were chosen based on the patient's problems.")

class LlmResponse(BaseModel):
    """The overall response structure for a batch of patients."""
    patient_results: Dict[str, KeywordResult] = Field(description="A dictionary where keys are patient_appointment_keys and values are the analysis results.")


def generate_prompt(patient_data: Dict[str, Any], keyword_list: List[str]) -> str:
    """Creates the detailed prompt for the LLM."""
    
    prompt_template = f"""
    You are an expert medical data analyst. Your task is to analyze clinical data for multiple patients and identify the most relevant keywords for an educational content campaign.

    **Instructions:**
    1.  For each patient provided in the 'PATIENT DATA' section below, review their list of 'Problems'.
    2.  From the 'AVAILABLE KEYWORDS' list, select the TOP 7 (seven) most relevant keywords that match the patient's problems.
    3.  Provide a concise, step-by-step reasoning for your keyword selection for each patient. The reasoning should directly link the selected keywords to specific problems listed for that patient.
    4.  You MUST ONLY choose keywords from the provided 'AVAILABLE KEYWORDS' list. Do not invent new ones.
    5.  Format your entire response as a single JSON object that strictly follows the provided JSON schema. The dictionary keys for 'patient_results' must exactly match the keys from the input 'PATIENT DATA' (e.g., 'PatientID_ApptID').

    **Available Keywords:**
    {json.dumps(keyword_list, indent=2)}

    **JSON Schema to Follow:**
    {json.dumps(LlmResponse.model_json_schema(), indent=2)}

    **Patient Data:**
    {json.dumps(patient_data, indent=2)}
    """
    return prompt_template

def call_api_gateway(prompt: str) -> Dict[str, Any]:
    """
    Calls the central API Routing Service to get the LLM response.
    """
    logger.info(f"Sending request to API Gateway at {config.API_GATEWAY_URL}")
    
    headers = {
        "Authorization": f"Bearer {config.API_GATEWAY_TOKEN}",
        "Content-Type": "application/json"
    }
    
    payload = {
        "prompt": prompt,
        "temperature": 0.1,  # Lower temperature for more deterministic, factual output
        "client_id": f"client-service-{config.DB_SERVER}" # Identify which client is calling
    }

    try:
        response = requests.post(
            config.API_GATEWAY_URL, 
            headers=headers, 
            json=payload, 
            timeout=300 # 5 minute timeout for potentially large batches
        )
        response.raise_for_status() # Raises an exception for 4xx or 5xx status codes
        
        # The API gateway returns a JSON object which includes the LLM's response
        gateway_response = response.json()
        
        # The actual LLM text is in the 'response' field. It might be a JSON string.
        llm_output_str = gateway_response.get("response", "{}")
        
        # Clean the response to ensure it's valid JSON
        # LLMs sometimes wrap their JSON in ```json ... ```
        if llm_output_str.strip().startswith("```json"):
            llm_output_str = llm_output_str.strip()[7:-4]

        return json.loads(llm_output_str)

    except requests.exceptions.RequestException as e:
        logger.error(f"Error calling API Gateway: {e}")
        raise ConnectionError(f"Failed to communicate with API Gateway: {e}") from e
    except json.JSONDecodeError as e:
        logger.error(f"Failed to parse JSON response from LLM: {e}")
        logger.error(f"Raw response was: {llm_output_str}")
        raise ValueError("LLM returned malformed JSON.") from e


def generate_keywords_for_batch(patient_data: Dict[str, Any], campaign_keywords: List[str]) -> LlmResponse:
    """
    Main function to generate keywords for a batch of patients using LangChain's principles.
    """
    if not patient_data:
        logger.warning("generate_keywords_for_batch called with no patient data.")
        return LlmResponse(patient_results={})

    # 1. Prompt Engineering
    prompt = generate_prompt(patient_data, campaign_keywords)

    # 2. Model Invocation (via API Gateway)
    raw_response_json = call_api_gateway(prompt)
    
    # 3. Output Parsing and Validation
    try:
        # Pydantic will automatically validate the structure and types
        validated_response = LlmResponse.parse_obj(raw_response_json)
        logger.info(f"Successfully parsed and validated LLM response for {len(validated_response.patient_results)} patients.")
        return validated_response
    except Exception as e:
        logger.error(f"Pydantic validation failed for LLM response: {e}")
        raise ValueError(f"LLM response did not match the required schema: {e}") from e