"""
Database connection pool management for the Campaign Keywords Pipeline.

This module provides connection pooling functionality to efficiently manage
database connections across the pipeline. It includes:
- Connection pool creation and management
- Connection health checking
- Graceful cleanup on shutdown
- Thread-safe connection handling
"""

import logging
import threading
from typing import Dict, Optional, Any
from contextlib import contextmanager
import pyodbc
from queue import Queue, Empty, Full

logger = logging.getLogger("database")

# Global connection pools registry
_connection_pools: Dict[str, 'ConnectionPool'] = {}
_pools_lock = threading.Lock()


class ConnectionPool:
    """
    A thread-safe database connection pool.
    
    This class manages a pool of database connections to avoid the overhead
    of creating and destroying connections for each database operation.
    """
    
    def __init__(
        self,
        connection_string: str,
        pool_name: str = "default",
        min_connections: int = 2,
        max_connections: int = 10,
        connection_timeout: int = 30,
        idle_timeout: int = 300  # 5 minutes
    ):
        """
        Initialize the connection pool.
        
        Args:
            connection_string: Database connection string
            pool_name: Name identifier for this pool
            min_connections: Minimum number of connections to maintain
            max_connections: Maximum number of connections allowed
            connection_timeout: Timeout for getting a connection from pool
            idle_timeout: Time before idle connections are closed
        """
        self.connection_string = connection_string
        self.pool_name = pool_name
        self.min_connections = min_connections
        self.max_connections = max_connections
        self.connection_timeout = connection_timeout
        self.idle_timeout = idle_timeout
        
        self._pool = Queue(maxsize=max_connections)
        self._active_connections = 0
        self._lock = threading.Lock()
        self._closed = False
        
        # Initialize minimum connections
        self._initialize_pool()
        
        logger.info(f"Connection pool '{pool_name}' initialized with {min_connections}-{max_connections} connections")
    
    def _initialize_pool(self):
        """Initialize the pool with minimum connections."""
        for _ in range(self.min_connections):
            try:
                conn = self._create_connection()
                self._pool.put(conn, block=False)
                self._active_connections += 1
            except Exception as e:
                logger.error(f"Failed to create initial connection for pool '{self.pool_name}': {e}")
    
    def _create_connection(self) -> pyodbc.Connection:
        """Create a new database connection."""
        try:
            conn = pyodbc.connect(self.connection_string, timeout=self.connection_timeout)
            conn.autocommit = False  # Use explicit transactions
            return conn
        except Exception as e:
            logger.error(f"Failed to create database connection: {e}")
            raise
    
    def _is_connection_valid(self, conn: pyodbc.Connection) -> bool:
        """Check if a connection is still valid."""
        try:
            # Simple query to test connection
            cursor = conn.cursor()
            cursor.execute("SELECT 1")
            cursor.fetchone()
            cursor.close()
            return True
        except Exception:
            return False
    
    @contextmanager
    def get_connection(self):
        """
        Get a connection from the pool using context manager.
        
        Usage:
            with pool.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT * FROM table")
                results = cursor.fetchall()
        """
        if self._closed:
            raise RuntimeError(f"Connection pool '{self.pool_name}' is closed")
        
        conn = None
        try:
            # Try to get an existing connection
            try:
                conn = self._pool.get(timeout=self.connection_timeout)
                
                # Validate the connection
                if not self._is_connection_valid(conn):
                    logger.warning(f"Invalid connection found in pool '{self.pool_name}', creating new one")
                    conn.close()
                    conn = self._create_connection()
                
            except Empty:
                # No connections available, create a new one if under limit
                with self._lock:
                    if self._active_connections < self.max_connections:
                        conn = self._create_connection()
                        self._active_connections += 1
                    else:
                        raise RuntimeError(f"Connection pool '{self.pool_name}' exhausted")
            
            yield conn
            
        except Exception as e:
            logger.error(f"Error with connection from pool '{self.pool_name}': {e}")
            if conn:
                try:
                    conn.rollback()  # Rollback any pending transaction
                except Exception:
                    pass
            raise
        finally:
            # Return connection to pool or close if pool is full/closed
            if conn:
                try:
                    if not self._closed and self._is_connection_valid(conn):
                        # Reset connection state
                        conn.rollback()  # Ensure clean state
                        
                        try:
                            self._pool.put(conn, block=False)
                        except Full:
                            # Pool is full, close this connection
                            conn.close()
                            with self._lock:
                                self._active_connections -= 1
                    else:
                        conn.close()
                        with self._lock:
                            self._active_connections -= 1
                except Exception as e:
                    logger.error(f"Error returning connection to pool '{self.pool_name}': {e}")
                    try:
                        conn.close()
                    except Exception:
                        pass
                    with self._lock:
                        self._active_connections -= 1
    
    def close(self):
        """Close all connections in the pool."""
        if self._closed:
            return
        
        self._closed = True
        logger.info(f"Closing connection pool '{self.pool_name}'")
        
        # Close all connections in the pool
        while not self._pool.empty():
            try:
                conn = self._pool.get_nowait()
                conn.close()
            except Empty:
                break
            except Exception as e:
                logger.error(f"Error closing connection in pool '{self.pool_name}': {e}")
        
        with self._lock:
            self._active_connections = 0
        
        logger.info(f"Connection pool '{self.pool_name}' closed")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get pool statistics."""
        return {
            "pool_name": self.pool_name,
            "active_connections": self._active_connections,
            "available_connections": self._pool.qsize(),
            "max_connections": self.max_connections,
            "min_connections": self.min_connections,
            "is_closed": self._closed
        }


def create_pool(
    connection_string: str,
    pool_name: str = "default",
    **kwargs
) -> ConnectionPool:
    """
    Create or get a connection pool.
    
    Args:
        connection_string: Database connection string
        pool_name: Name identifier for this pool
        **kwargs: Additional arguments for ConnectionPool
    
    Returns:
        ConnectionPool instance
    """
    with _pools_lock:
        if pool_name in _connection_pools:
            logger.warning(f"Connection pool '{pool_name}' already exists, returning existing pool")
            return _connection_pools[pool_name]
        
        pool = ConnectionPool(connection_string, pool_name, **kwargs)
        _connection_pools[pool_name] = pool
        return pool


def get_pool(pool_name: str = "default") -> Optional[ConnectionPool]:
    """
    Get an existing connection pool by name.
    
    Args:
        pool_name: Name of the pool to retrieve
    
    Returns:
        ConnectionPool instance or None if not found
    """
    with _pools_lock:
        return _connection_pools.get(pool_name)


def close_pool(pool_name: str = "default"):
    """
    Close and remove a specific connection pool.
    
    Args:
        pool_name: Name of the pool to close
    """
    with _pools_lock:
        if pool_name in _connection_pools:
            pool = _connection_pools.pop(pool_name)
            pool.close()


def close_all_pools():
    """Close all connection pools."""
    with _pools_lock:
        for pool_name, pool in list(_connection_pools.items()):
            try:
                pool.close()
            except Exception as e:
                logger.error(f"Error closing pool '{pool_name}': {e}")
        
        _connection_pools.clear()
        logger.info("All connection pools closed")


def get_all_pool_stats() -> Dict[str, Dict[str, Any]]:
    """Get statistics for all connection pools."""
    with _pools_lock:
        return {name: pool.get_stats() for name, pool in _connection_pools.items()}


if __name__ == "__main__":
    # Test the connection pool
    import config
    
    logging.basicConfig(level=logging.INFO)
    
    # Create a test pool
    conn_string = config.config.get_database_connection_string()
    pool = create_pool(conn_string, "test_pool", min_connections=1, max_connections=3)
    
    # Test getting connections
    try:
        with pool.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT 1 as test_value")
            result = cursor.fetchone()
            print(f"Test query result: {result}")
        
        print("Pool stats:", pool.get_stats())
        
    finally:
        close_all_pools()
