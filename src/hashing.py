"""
Hashing and data canonicalization utilities for change detection.

This module provides utilities for:
- Creating stable hashes of patient data for change detection
- Canonicalizing patient data structures for consistent hashing
- Token counting for LLM cost estimation
- Patient ID hashing for privacy

The hashing functions ensure that identical clinical data produces identical
hashes, enabling efficient detection of data changes between pipeline runs.
"""

import hashlib
import json
from typing import Any, Dict, List

def hash_patient_id(pid) -> str:
    """
    Create a privacy-preserving hash of a patient ID.

    Generates a shortened SHA-256 hash of the patient ID with a "PID_" prefix
    for easy identification. Useful for logging and debugging while maintaining
    patient privacy.

    Args:
        pid: Patient ID (any type that can be converted to string)

    Returns:
        str: Hashed patient ID with "PID_" prefix (16 characters total)

    Example:
        >>> hash_patient_id("12345")
        'PID_a665a45920'
        >>> hash_patient_id(67890)
        'PID_b1946ac92d'
    """
    return "PID_" + hashlib.sha256(str(pid).encode()).hexdigest()[:12]

def _sort_list_of_dicts(lst: List[dict], keys: tuple[str, ...]) -> List[dict]:
    """
    Sort a list of dictionaries by specified keys for canonical ordering.

    Creates a stable sort order for lists of dictionaries to ensure consistent
    hashing. Missing keys are treated as empty strings.

    Args:
        lst (List[dict]): List of dictionaries to sort
        keys (tuple[str, ...]): Keys to sort by, in order of priority

    Returns:
        List[dict]: Sorted list of dictionaries

    Example:
        >>> data = [{"name": "B", "value": "2"}, {"name": "A", "value": "1"}]
        >>> _sort_list_of_dicts(data, ("name",))
        [{'name': 'A', 'value': '1'}, {'name': 'B', 'value': '2'}]
    """
    return sorted(lst, key=lambda d: tuple((d.get(k) or "").strip() for k in keys))

def canonicalize_patient_data(d: Dict[str, Any]) -> Dict[str, Any]:
    """
    Convert patient data to a canonical form for consistent hashing.

    Sorts all lists and removes empty values to ensure that semantically
    identical patient data produces identical hashes. This enables reliable
    change detection between pipeline runs.

    Args:
        d (Dict[str, Any]): Raw patient data dictionary with keys:
            - Problems: List of medical problems/diagnoses

    Returns:
        Dict[str, Any]: Canonicalized patient data with sorted lists

    Example:
        >>> data = {
        ...     "Problems": [{"ProblemName": "B"}, {"ProblemName": "A"}]
        ... }
        >>> canonical = canonicalize_patient_data(data)
        >>> canonical["Problems"][0]["ProblemName"]
        'A'
    """
    return {
        "Problems": _sort_list_of_dicts(
            d.get("Problems", []), ("ProblemName", "Type", "DetailText")
        ),
    }

def stable_hash(obj: Any) -> str:
    """
    Generate a stable SHA-256 hash of any JSON-serializable object.

    Creates a deterministic hash by serializing the object to JSON with
    sorted keys and consistent formatting. Identical objects will always
    produce identical hashes. Handles datetime objects by converting them to ISO format.

    Args:
        obj (Any): Any JSON-serializable object (including datetime objects)

    Returns:
        str: 64-character hexadecimal SHA-256 hash

    Example:
        >>> stable_hash({"b": 2, "a": 1})
        'ed7002b439e9ac845f22357d822bac1444730fbdb6016d3ec9432297b9ec9f73'
        >>> stable_hash({"a": 1, "b": 2})  # Same hash despite different order
        'ed7002b439e9ac845f22357d822bac1444730fbdb6016d3ec9432297b9ec9f73'
    """
    from datetime import datetime

    def datetime_serializer(obj):
        """JSON serializer for datetime objects"""
        if isinstance(obj, datetime):
            return obj.isoformat()
        raise TypeError(f"Object of type {type(obj)} is not JSON serializable")

    return hashlib.sha256(
        json.dumps(obj, sort_keys=True, separators=(",", ":"), default=datetime_serializer).encode()
    ).hexdigest()

# Token counting with tiktoken fallback
try:
    import tiktoken
    TIKTOKEN_AVAILABLE = True

    def count_tokens(text: str, model: str = "gpt-3.5-turbo") -> int:
        """
        Count tokens in text using tiktoken for accurate LLM token counting.

        Uses the tiktoken library to count tokens exactly as the LLM would,
        enabling accurate cost estimation and limit checking.

        Args:
            text (str): Text to count tokens for
            model (str): Model name for tokenizer selection. Defaults to "gpt-3.5-turbo".

        Returns:
            int: Number of tokens in the text

        Example:
            >>> count_tokens("Hello world!")
            3
        """
        # Convert to string if not already
        if not isinstance(text, str):
            text = str(text)

        try:
            enc = tiktoken.encoding_for_model(model)
            return len(enc.encode(text))
        except Exception:
            # Fallback to cl100k_base encoding if model-specific encoding fails
            enc = tiktoken.get_encoding("cl100k_base")
            return len(enc.encode(text))

except ImportError:
    TIKTOKEN_AVAILABLE = False
    import logging

    def count_tokens(text: str, _: str | None = None) -> int:  # type: ignore[arg-type]
        """
        Improved fallback token counting when tiktoken is not available.

        Uses a more accurate estimation based on character count and word count
        rather than simple word splitting. Based on OpenAI's rule of thumb.

        Args:
            text (str): Text to count tokens for
            model (str | None): Ignored in fallback implementation

        Returns:
            int: Estimated number of tokens

        Example:
            >>> count_tokens("Hello world!")
            3
        """
        if not isinstance(text, str):
            text = str(text)

        if not text.strip():
            return 0

        # Log warning on first use
        logger = logging.getLogger("patient_pipeline")
        if not hasattr(count_tokens, '_warned'):
            logger.warning("Using fallback token counting. Install tiktoken for accurate counts.")
            count_tokens._warned = True  # type: ignore

        # Improved estimation: ~4 characters per token on average
        char_count = len(text)
        word_count = len(text.split())

        # Character-based estimate (OpenAI rule of thumb)
        char_estimate = char_count / 4

        # Word-based estimate (words are typically 1.3 tokens on average)
        word_estimate = word_count * 1.3

        # Use the higher estimate to be conservative for cost limits
        return int(max(char_estimate, word_estimate))