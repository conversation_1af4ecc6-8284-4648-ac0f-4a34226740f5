"""
Logging configuration module for the Campaign Keywords Pipeline.

This module provides centralized logging configuration with:
- Rotating file handlers to prevent log files from growing too large
- Console output for development and debugging
- Structured logging with consistent formatting
- Multiple loggers for different components
"""

import logging
import logging.handlers
import os
import sys
from pathlib import Path
from typing import Dict


def setup_pipeline_logging(
    log_level: str = "INFO",
    log_dir: str = "logs",
    max_file_size: int = 10 * 1024 * 1024,  # 10MB
    backup_count: int = 5,
    console_output: bool = True
) -> Dict[str, logging.Logger]:
    """
    Set up logging configuration for the pipeline.
    
    Args:
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_dir: Directory to store log files
        max_file_size: Maximum size of each log file in bytes
        backup_count: Number of backup log files to keep
        console_output: Whether to also output logs to console
    
    Returns:
        Dict of configured loggers by name
    """
    # Create logs directory if it doesn't exist
    log_path = Path(log_dir)
    log_path.mkdir(exist_ok=True)
    
    # Convert log level string to logging constant
    numeric_level = getattr(logging, log_level.upper(), logging.INFO)
    
    # Create formatters
    detailed_formatter = logging.Formatter(
        fmt='%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    simple_formatter = logging.Formatter(
        fmt='%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(numeric_level)
    
    # Clear any existing handlers
    root_logger.handlers.clear()
    
    # Console handler
    if console_output:
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(numeric_level)
        console_handler.setFormatter(simple_formatter)
        root_logger.addHandler(console_handler)
    
    # Create specific loggers for different components
    loggers = {}
    
    # Main pipeline logger
    main_logger = logging.getLogger("main")
    main_file_handler = logging.handlers.RotatingFileHandler(
        log_path / "main.log",
        maxBytes=max_file_size,
        backupCount=backup_count
    )
    main_file_handler.setLevel(numeric_level)
    main_file_handler.setFormatter(detailed_formatter)
    main_logger.addHandler(main_file_handler)
    main_logger.setLevel(numeric_level)
    loggers['main'] = main_logger
    
    # Patient pipeline logger
    pipeline_logger = logging.getLogger("patient_pipeline")
    pipeline_file_handler = logging.handlers.RotatingFileHandler(
        log_path / "pipeline.log",
        maxBytes=max_file_size,
        backupCount=backup_count
    )
    pipeline_file_handler.setLevel(numeric_level)
    pipeline_file_handler.setFormatter(detailed_formatter)
    pipeline_logger.addHandler(pipeline_file_handler)
    pipeline_logger.setLevel(numeric_level)
    loggers['pipeline'] = pipeline_logger
    
    # Database logger
    db_logger = logging.getLogger("database")
    db_file_handler = logging.handlers.RotatingFileHandler(
        log_path / "database.log",
        maxBytes=max_file_size,
        backupCount=backup_count
    )
    db_file_handler.setLevel(numeric_level)
    db_file_handler.setFormatter(detailed_formatter)
    db_logger.addHandler(db_file_handler)
    db_logger.setLevel(numeric_level)
    loggers['database'] = db_logger
    
    # Health check logger
    health_logger = logging.getLogger("health_check")
    health_file_handler = logging.handlers.RotatingFileHandler(
        log_path / "health_check.log",
        maxBytes=max_file_size,
        backupCount=backup_count
    )
    health_file_handler.setLevel(numeric_level)
    health_file_handler.setFormatter(detailed_formatter)
    health_logger.addHandler(health_file_handler)
    health_logger.setLevel(numeric_level)
    loggers['health_check'] = health_logger
    
    # API logger
    api_logger = logging.getLogger("api")
    api_file_handler = logging.handlers.RotatingFileHandler(
        log_path / "api.log",
        maxBytes=max_file_size,
        backupCount=backup_count
    )
    api_file_handler.setLevel(numeric_level)
    api_file_handler.setFormatter(detailed_formatter)
    api_logger.addHandler(api_file_handler)
    api_logger.setLevel(numeric_level)
    loggers['api'] = api_logger
    
    # Log the configuration
    main_logger.info(f"Logging configured with level: {log_level}")
    main_logger.info(f"Log directory: {log_path.absolute()}")
    main_logger.info(f"Max file size: {max_file_size / (1024*1024):.1f}MB")
    main_logger.info(f"Backup count: {backup_count}")
    main_logger.info(f"Console output: {console_output}")
    
    return loggers


def get_logger(name: str) -> logging.Logger:
    """
    Get a logger by name. If it doesn't exist, create it with default configuration.
    
    Args:
        name: Name of the logger
    
    Returns:
        Configured logger instance
    """
    return logging.getLogger(name)


if __name__ == "__main__":
    # Test the logging configuration
    loggers = setup_pipeline_logging(log_level="DEBUG")
    
    # Test each logger
    for logger_name, logger in loggers.items():
        logger.info(f"Testing {logger_name} logger")
        logger.debug(f"Debug message from {logger_name}")
        logger.warning(f"Warning message from {logger_name}")
    
    print("Logging test complete. Check the logs/ directory for output files.")
