-- Stored Procedures for Campaign Keywords Pipeline
-- Database: V_820_Dev
-- Table: dbo.Appointment_EducationalContent_Keyword
--
-- IMPORTANT: This script includes migration logic to update existing stored procedures
-- Run this entire script against your database to ensure all procedures are up to date

-- =============================================
-- MIGRATION: Drop and recreate sp_GetAppointmentBatch to handle parameter change
-- This ensures the stored procedure is updated from @StartDate to @ProcessDate
-- =============================================
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_GetAppointmentBatch')
    DROP PROCEDURE sp_GetAppointmentBatch;
GO

-- =============================================
-- Stored Procedure: sp_GetAppointmentBatch
-- Description: Fetch a batch of appointment records with pagination
--              Filters by CreatedUTCDTTM to process appointments created on a specific date
-- Parameters:
--   @Offset: Number of records to skip
--   @BatchSize: Number of records to return
--   @ProcessDate: Optional date filter to process appointments created on this date (can be NULL)
-- Returns: PatientID, ApptID, AppNo, ApptDate (from Appointments.ActualAppDate if available)
-- =============================================
CREATE OR ALTER PROCEDURE sp_GetAppointmentBatch
    @Offset INT,
    @BatchSize INT,
    @ProcessDate DATETIME = NULL
AS
BEGIN
    SET NOCOUNT ON;

    SELECT DISTINCT
        aek.PatientID,
        aek.ApptID,
        aek.AppNo,
        a.ActualAppDate AS ApptDate  -- Get ActualAppDate from Appointments table
    FROM dbo.Appointment_EducationalContent_Keyword aek
    LEFT JOIN dbo.Appointments a ON aek.ApptID = a.AppointmentId  -- Join on ApptID = AppointmentId
    WHERE (@ProcessDate IS NULL OR 
           (CAST(aek.CreatedUTCDTTM AS DATE) = CAST(@ProcessDate AS DATE)))
    ORDER BY aek.PatientID, aek.ApptID
    OFFSET @Offset ROWS
    FETCH NEXT @BatchSize ROWS ONLY;
END
GO

-- =============================================
-- Stored Procedure: sp_GetPatientClinicalData
-- Description: Fetch comprehensive clinical data for a single patient
-- Parameters:
--   @PatientID: Patient identifier
-- =============================================
CREATE OR ALTER PROCEDURE sp_GetPatientClinicalData
    @PatientID BIGINT
AS
BEGIN
    SET NOCOUNT ON;

    -- Get all clinical data for the patient using separate queries
    -- Results
    SELECT
        'Result' as DataType,
        ResultName as Name,
        ObservationValue as Value1,
        ObservationUnit as Value2,
        NULL as Value3
    FROM dbo.Patient_Result
    WHERE PatientID = @PatientID

    UNION ALL

    -- Problems
    SELECT
        'Problem' as DataType,
        ProblemName as Name,
        Type as Value1,
        DetailText as Value2,
        NULL as Value3
    FROM dbo.Patient_Problem
    WHERE PatientID = @PatientID

    UNION ALL

    -- Medications
    SELECT
        'Medication' as DataType,
        MedicationName as Name,
        MedType as Value1,
        CAST(DoseQuantity as NVARCHAR(50)) as Value2,
        NULL as Value3
    FROM dbo.Patient_Medication
    WHERE PatientID = @PatientID

    UNION ALL

    -- Allergies
    SELECT
        'Allergy' as DataType,
        AllergyName as Name,
        NULL as Value1,
        NULL as Value2,
        NULL as Value3
    FROM dbo.Patient_Allergy
    WHERE PatientID = @PatientID;
END

-- =============================================
-- HASH-BASED CHANGE DETECTION SYSTEM NOTES:
-- =============================================
-- 1. sp_UpdateAppointmentKeywords now supports Reasoning, Hashkey, and automatic KeywordLastUpdated
-- 2. sp_GetPatientLatestHash retrieves the most recent hash for a patient
-- 3. sp_GetAppointmentBatch now includes AppNo and ApptDate directly from the table
-- 4. System automatically updates appointments with previous results when clinical data hasn't changed
-- 5. This prevents NULL values and avoids unnecessary LLM calls for unchanged patients
-- 6. All appointments for the same patient with unchanged clinical data get the same keywords/hash
-- 7. All appointment data is available directly from dbo.Appointment_EducationalContent_Keyword
GO

-- =============================================
-- Stored Procedure: sp_UpdateAppointmentKeywords
-- Description: Update keywords for specific appointment records
-- Parameters:
--   @PatientID: Patient identifier
--   @ApptID: Appointment identifier
--   @Keywords: Generated keywords (JSON or comma-separated)
--   @Reasoning: LLM reasoning for keyword selection
--   @Hashkey: Hash of patient clinical data for change detection
--   @Source: Source of keywords (e.g., 'LLM')
--   @KeywordSource: Source system (e.g., 'Gemini')
--   @ModifiedBy: User ID who modified the record
-- =============================================
CREATE OR ALTER PROCEDURE sp_UpdateAppointmentKeywords
    @PatientID BIGINT,
    @ApptID BIGINT,
    @Keywords NVARCHAR(2000),
    @Reasoning NVARCHAR(MAX) = NULL,
    @Hashkey NVARCHAR(64) = NULL,
    @Source NVARCHAR(10) = 'LLM',
    @KeywordSource NVARCHAR(25) = 'Gemini',
    @ModifiedBy BIGINT = 1
AS
BEGIN
    SET NOCOUNT ON;

    UPDATE dbo.Appointment_EducationalContent_Keyword
    SET
        [Keyword] = @Keywords,
        Reasoning = @Reasoning,
        Hashkey = @Hashkey,
        KeywordLastUpdated = GETDATE(),
        [Source] = @Source,
        [KeywordSource] = @KeywordSource,
        [ModifiedBy] = @ModifiedBy,
        [ModifiedUTCDTTM] = GETUTCDATE()
    WHERE PatientID = @PatientID
      AND ApptID = @ApptID;

    -- Return number of rows affected
    SELECT @@ROWCOUNT as RowsAffected;
END
GO

-- =============================================
-- Stored Procedure: sp_GetPatientLatestHash
-- Description: Get the most recent hash value for a patient's clinical data
-- Parameters:
--   @PatientID: Patient identifier
-- Returns: Latest Hashkey and KeywordLastUpdated for the patient
-- =============================================
CREATE OR ALTER PROCEDURE sp_GetPatientLatestHash
    @PatientID BIGINT
AS
BEGIN
    SET NOCOUNT ON;

    SELECT TOP 1
        Hashkey,
        KeywordLastUpdated
    FROM dbo.Appointment_EducationalContent_Keyword
    WHERE PatientID = @PatientID
      AND Hashkey IS NOT NULL
    ORDER BY KeywordLastUpdated DESC;
END

GO

-- =============================================
-- Stored Procedure: sp_BatchUpdateAppointmentKeywords
-- Description: Update keywords for multiple appointment records in a batch
-- Parameters: 
--   @KeywordUpdates: Table-valued parameter containing batch updates
-- =============================================
-- First create the table type for batch updates
CREATE TYPE KeywordUpdateType AS TABLE
(
    PatientID BIGINT,
    ApptID BIGINT,
    Keywords NVARCHAR(2000),
    Source NVARCHAR(10),
    KeywordSource NVARCHAR(25)
);
GO

CREATE OR ALTER PROCEDURE sp_BatchUpdateAppointmentKeywords
    @KeywordUpdates KeywordUpdateType READONLY,
    @ModifiedBy BIGINT = 1
AS
BEGIN
    SET NOCOUNT ON;
    
    UPDATE aec
    SET 
        [Keyword] = ku.Keywords,
        [Source] = ku.Source,
        [KeywordSource] = ku.KeywordSource,
        [ModifiedBy] = @ModifiedBy,
        [ModifiedUTCDTTM] = GETUTCDATE()
    FROM dbo.Appointment_EducationalContent_Keyword aec
    INNER JOIN @KeywordUpdates ku ON aec.PatientID = ku.PatientID 
                                  AND aec.ApptID = ku.ApptID;
    
    -- Return number of rows affected
    SELECT @@ROWCOUNT as RowsAffected;
END
GO

-- =============================================
-- Stored Procedure: sp_GetAppointmentsByPatientIDs
-- Description: Get appointment records for specific patient IDs
-- Parameters: 
--   @PatientIDs: Comma-separated list of patient IDs
-- =============================================
CREATE OR ALTER PROCEDURE sp_GetAppointmentsByPatientIDs
    @PatientIDs NVARCHAR(MAX)
AS
BEGIN
    SET NOCOUNT ON;
    
    -- Split the comma-separated patient IDs and convert to table
    WITH PatientIDList AS (
        SELECT CAST(value AS BIGINT) AS PatientID
        FROM STRING_SPLIT(@PatientIDs, ',')
        WHERE value != ''
    )
    SELECT 
        aec.PatientID,
        aec.ApptID,
        aec.AppNo,
        aec.ApptDate,
        aec.[Keyword] as ExistingKeywords
    FROM dbo.Appointment_EducationalContent_Keyword aec
    INNER JOIN PatientIDList pil ON aec.PatientID = pil.PatientID
    ORDER BY aec.PatientID, aec.ApptID;
END
GO
