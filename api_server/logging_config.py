"""
Logging configuration module for the API Server.

This module provides logging configuration for the API server with:
- Single rotating file handler for all API server logs
- Console output for development and debugging
- Structured logging with consistent formatting
"""

import logging
import logging.handlers
import os
import sys
from pathlib import Path


def setup_api_server_logging(
    log_level: str = "INFO",
    log_dir: str = "logs",
    log_filename: str = "api_server.log",
    max_file_size: int = 50 * 1024 * 1024,  # 50MB
    backup_count: int = 5,
    console_output: bool = True
) -> logging.Logger:
    """
    Set up logging configuration for the API server.
    
    Args:
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_dir: Directory to store log files
        log_filename: Name of the log file
        max_file_size: Maximum size of each log file in bytes
        backup_count: Number of backup log files to keep
        console_output: Whether to also output logs to console
    
    Returns:
        Configured logger instance
    """
    # Create logs directory if it doesn't exist
    log_path = Path(log_dir)
    log_path.mkdir(exist_ok=True)
    
    # Convert log level string to logging constant
    numeric_level = getattr(logging, log_level.upper(), logging.INFO)
    
    # Create formatter
    formatter = logging.Formatter(
        fmt='%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    console_formatter = logging.Formatter(
        fmt='%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(numeric_level)
    
    # Clear any existing handlers
    root_logger.handlers.clear()
    
    # File handler for all API server logs
    file_handler = logging.handlers.RotatingFileHandler(
        log_path / log_filename,
        maxBytes=max_file_size,
        backupCount=backup_count
    )
    file_handler.setLevel(numeric_level)
    file_handler.setFormatter(formatter)
    root_logger.addHandler(file_handler)
    
    # Console handler
    if console_output:
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(numeric_level)
        console_handler.setFormatter(console_formatter)
        root_logger.addHandler(console_handler)
    
    # Create main logger for the API server
    logger = logging.getLogger("api_server")
    logger.setLevel(numeric_level)
    
    # Configure uvicorn loggers to use our handlers
    uvicorn_logger = logging.getLogger("uvicorn")
    uvicorn_logger.handlers = []
    uvicorn_logger.setLevel(numeric_level)
    
    uvicorn_access_logger = logging.getLogger("uvicorn.access")
    uvicorn_access_logger.handlers = []
    uvicorn_access_logger.setLevel(numeric_level)
    
    # Log the configuration
    logger.info(f"API server logging configured with level: {log_level}")
    logger.info(f"Log file: {log_path / log_filename}")
    logger.info(f"Max file size: {max_file_size / (1024*1024):.1f}MB")
    logger.info(f"Backup count: {backup_count}")
    logger.info(f"Console output: {console_output}")
    
    return logger


def get_logger(name: str = "api_server") -> logging.Logger:
    """
    Get a logger by name for the API server.
    
    Args:
        name: Name of the logger (defaults to "api_server")
    
    Returns:
        Configured logger instance
    """
    return logging.getLogger(name)


if __name__ == "__main__":
    # Test the logging configuration
    logger = setup_api_server_logging(log_level="DEBUG")
    
    # Test the logger
    logger.info("Testing API server logger")
    logger.debug("Debug message from API server")
    logger.warning("Warning message from API server")
    logger.error("Error message from API server")
    
    # Test uvicorn loggers
    uvicorn_logger = logging.getLogger("uvicorn")
    uvicorn_logger.info("Testing uvicorn logger")
    
    uvicorn_access_logger = logging.getLogger("uvicorn.access")
    uvicorn_access_logger.info("Testing uvicorn access logger")
    
    print("API server logging test complete. Check the logs/ directory for output files.")
