"""
Main entry point for the Campaign Keywords Pipeline.

This module serves as the entry point for the patient keyword extraction pipeline.
It imports and executes the main pipeline function that processes patient data
and generates campaign keywords using LLM analysis.

Database Architecture:
- Configuration Database: Stores system settings (CONFIG_DB_* in config.py)
- Keyword Database: Stores patient data and generated keywords (DB_* in config.py)

The configuration database can dynamically override keyword database connection
settings through the EducationalContent_KeywordConfig table.

Usage:
    python main.py

The pipeline will:
1. Load configuration from the configuration database
2. Connect to the keyword database (using settings from config DB if available)
3. Load campaign keywords from CSV
4. Fetch patient data from the keyword database in batches
5. Use hash-based change detection to process only modified patients
6. Generate keywords using LLM (Gemini)
7. Store results in the keyword database
"""

from src.pipeline import main
from src.health_check import start_health_api
from src.logging_config import setup_pipeline_logging
from src.db_pool import close_all_pools
import signal
import sys
import logging

# Setup logging with rotation
loggers = setup_pipeline_logging()
logger = loggers['main']

def shutdown_handler(signum, frame):
    """Handle shutdown signals gracefully."""
    logger.info(f"Received signal {signum}. Initiating graceful shutdown...")
    
    # Close database connection pools
    logger.info("Closing database connection pools...")
    close_all_pools()
    
    logger.info("Shutdown complete.")
    sys.exit(0)

if __name__ == "__main__":
    # Register shutdown handlers
    signal.signal(signal.SIGINT, shutdown_handler)
    signal.signal(signal.SIGTERM, shutdown_handler)
    
    try:
        # Start health check API
        logger.info("Starting health check API...")
        start_health_api(port=8080)
        
        # Execute the main pipeline process
        logger.info("Starting main pipeline...")
        main()
        
    except Exception as e:
        logger.exception(f"Fatal error in main: {e}")
        sys.exit(1)
    finally:
        # Cleanup
        logger.info("Cleaning up resources...")
        close_all_pools()
        logger.info("Pipeline execution complete.")
