"""
Configuration module for the Campaign Keywords Pipeline.

This module handles all configuration settings for the pipeline including:
- Database connection settings
- API Gateway configuration
- Pipeline processing parameters
- Environment variable loading with validation

All sensitive configuration values should be provided via environment variables
or a .env file for security.
"""

import os
import sys
import logging
from pathlib import Path
from typing import Optional
from datetime import datetime

# Try to import python-dotenv, but make it optional
try:
    from dotenv import load_dotenv
    load_dotenv()
    DOTENV_AVAILABLE = True
except ImportError:
    DOTENV_AVAILABLE = False
    logging.warning(
        "python-dotenv not available. Environment variables will be loaded from system only. "
        "Install with: pip install python-dotenv"
    )


class ConfigurationError(Exception):
    """Raised when there are configuration validation errors."""
    pass


class Config:
    """
    Configuration class that loads and validates all application settings.

    This class centralizes configuration management and provides validation
    for required settings. It loads values from environment variables with
    sensible defaults where appropriate.
    """

    def __init__(self):
        """Initialize configuration and validate required settings."""
        self._load_database_config()
        self._load_api_config()
        self._load_pipeline_config()
        self._validate_config()

    def _load_database_config(self) -> None:
        """Load database configuration from environment variables."""
        # Database Configuration for the Client DB
        self.DB_DRIVER = os.getenv("DB_DRIVER", "{ODBC Driver 17 for SQL Server}")
        self.DB_SERVER = os.getenv("DB_SERVER", "your_client_db_server.database.windows.net")
        self.DB_DATABASE = os.getenv("DB_DATABASE", "your_client_database_name")
        self.DB_USERNAME = os.getenv("DB_USERNAME", "your_username")
        self.DB_PASSWORD = os.getenv("DB_PASSWORD", "your_password")
        self.DB_ENCRYPT = os.getenv("DB_ENCRYPT", "yes")  # 'yes' is recommended for Azure SQL
        self.DB_TRUST_CERT = os.getenv("DB_TRUST_CERT", "no")

    def _load_api_config(self) -> None:
        """Load API Gateway configuration from environment variables."""
        # API Gateway (IPC) Configuration
        self.API_GATEWAY_URL = os.getenv(
            "API_GATEWAY_URL",
            "http://your_api_gateway_ip:8000/gemini/query"
        )
        self.API_GATEWAY_TOKEN = os.getenv("API_GATEWAY_TOKEN", "your_jwt_token")

    def _load_pipeline_config(self) -> None:
        """Load pipeline processing configuration."""
        # Pipeline Settings
        self.BATCH_SIZE = int(os.getenv("BATCH_SIZE", "50"))

        # Process date - can be None for all dates or 'YYYY-MM-DD' string for specific date
        process_date_str = os.getenv("PROCESS_DATE")
        self.PROCESS_DATE: Optional[str] = None
        if process_date_str:
            try:
                # Validate date format
                datetime.strptime(process_date_str, "%Y-%m-%d")
                self.PROCESS_DATE = process_date_str
            except ValueError:
                raise ConfigurationError(
                    f"PROCESS_DATE must be in YYYY-MM-DD format, got: {process_date_str}"
                )

        # Campaign keywords CSV file path
        csv_path = os.getenv("CAMPAIGN_KEYWORDS_CSV", "keywords.csv")
        self.CAMPAIGN_KEYWORDS_CSV = str(Path(csv_path).resolve())

    def _validate_config(self) -> None:
        """Validate configuration and warn about placeholder values."""
        warnings = []
        errors = []

        # Check for placeholder values that should be replaced
        placeholder_checks = [
            ("DB_SERVER", "your_client_db_server.database.windows.net"),
            ("DB_DATABASE", "your_client_database_name"),
            ("DB_USERNAME", "your_username"),
            ("DB_PASSWORD", "your_password"),
            ("API_GATEWAY_URL", "http://your_api_gateway_ip:8000/gemini/query"),
            ("API_GATEWAY_TOKEN", "your_jwt_token"),
        ]

        for attr_name, placeholder_value in placeholder_checks:
            if getattr(self, attr_name) == placeholder_value:
                warnings.append(f"{attr_name} is using placeholder value: {placeholder_value}")

        # Check if keywords CSV file exists
        if not Path(self.CAMPAIGN_KEYWORDS_CSV).exists():
            warnings.append(f"Keywords CSV file not found: {self.CAMPAIGN_KEYWORDS_CSV}")

        # Validate batch size
        if self.BATCH_SIZE <= 0:
            errors.append(f"BATCH_SIZE must be positive, got: {self.BATCH_SIZE}")

        # Log warnings
        if warnings:
            logging.warning("Configuration warnings found:")
            for warning in warnings:
                logging.warning(f"  - {warning}")

        # Raise errors
        if errors:
            error_msg = "Configuration errors found:\n" + "\n".join(f"  - {error}" for error in errors)
            raise ConfigurationError(error_msg)

    def get_database_connection_string(self) -> str:
        """
        Generate database connection string for SQL Server.

        Returns:
            str: Formatted connection string for pyodbc/SQLAlchemy
        """
        return (
            f"DRIVER={self.DB_DRIVER};"
            f"SERVER={self.DB_SERVER};"
            f"DATABASE={self.DB_DATABASE};"
            f"UID={self.DB_USERNAME};"
            f"PWD={self.DB_PASSWORD};"
            f"Encrypt={self.DB_ENCRYPT};"
            f"TrustServerCertificate={self.DB_TRUST_CERT};"
        )

    def __repr__(self) -> str:
        """String representation of config (without sensitive data)."""
        return (
            f"Config("
            f"DB_SERVER='{self.DB_SERVER}', "
            f"DB_DATABASE='{self.DB_DATABASE}', "
            f"BATCH_SIZE={self.BATCH_SIZE}, "
            f"PROCESS_DATE={self.PROCESS_DATE}"
            f")"
        )


# Create global configuration instance
try:
    config = Config()
except ConfigurationError as e:
    logging.error(f"Configuration error: {e}")
    sys.exit(1)

# Export individual settings for backward compatibility
DB_DRIVER = config.DB_DRIVER
DB_SERVER = config.DB_SERVER
DB_DATABASE = config.DB_DATABASE
DB_USERNAME = config.DB_USERNAME
DB_PASSWORD = config.DB_PASSWORD
DB_ENCRYPT = config.DB_ENCRYPT
DB_TRUST_CERT = config.DB_TRUST_CERT

API_GATEWAY_URL = config.API_GATEWAY_URL
API_GATEWAY_TOKEN = config.API_GATEWAY_TOKEN

BATCH_SIZE = config.BATCH_SIZE
PROCESS_DATE = config.PROCESS_DATE
CAMPAIGN_KEYWORDS_CSV = config.CAMPAIGN_KEYWORDS_CSV