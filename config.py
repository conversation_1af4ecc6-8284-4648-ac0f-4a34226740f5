    # config.py
import os
from dotenv import load_dotenv

load_dotenv()

# --- Database Configuration for the Client DB ---
# Use environment variables for security
DB_DRIVER = os.getenv("DB_DRIVER", "{ODBC Driver 17 for SQL Server}")
DB_SERVER = os.getenv("DB_SERVER", "your_client_db_server.database.windows.net")
DB_DATABASE = os.getenv("DB_DATABASE", "your_client_database_name")
DB_USERNAME = os.getenv("DB_USERNAME", "your_username")
DB_PASSWORD = os.getenv("DB_PASSWORD", "your_password")
DB_ENCRYPT = os.getenv("DB_ENCRYPT", "yes") # 'yes' is recommended for Azure SQL
DB_TRUST_CERT = os.getenv("DB_TRUST_CERT", "no")

# --- API Gateway (IPC) Configuration ---
# The URL of your central API Routing Service (api_server.py)
API_GATEWAY_URL = os.getenv("API_GATEWAY_URL", "http://your_api_gateway_ip:8000/gemini/query")
# JWT token for authenticating with the gateway. This should be securely managed.
API_GATEWAY_TOKEN = os.getenv("API_GATEWAY_TOKEN", "your_jwt_token")


# --- Pipeline Settings ---
BATCH_SIZE = 50  # Number of appointments to process in one go
PROCESS_DATE = None # Set to a 'YYYY-MM-DD' string to process a specific date, or None for all
CAMPAIGN_KEYWORDS_CSV = "keywords.csv" # Path to your list of 160 keywords